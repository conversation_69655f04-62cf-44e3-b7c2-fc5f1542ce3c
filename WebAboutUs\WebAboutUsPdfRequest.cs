
namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// WebAboutUs PDF generation request containing about us data and configuration
/// </summary>
public class WebAboutUsPdfRequest : PdfRequest
{
    // About Us Information
    public string AboutUs { get; set; } = string.Empty;
    public string OurApproach { get; set; } = string.Empty;
    public List<WebAboutUsStatistic> Statistics { get; set; } = new();
    public List<WebAboutUsSlide> Slides { get; set; } = new();
    public List<WebAboutUsService> Services { get; set; } = new();

}

public class WebAboutUsSlide
{
    public string Title { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public int OrderFlag { get; set; }
    public string Caption { get; set; } = string.Empty;
}

public class WebAboutUsStatistic
{
    public string Value { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;

}

public class WebAboutUsService
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
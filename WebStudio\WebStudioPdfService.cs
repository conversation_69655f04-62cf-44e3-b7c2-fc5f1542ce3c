using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebStudio;

/// <summary>
/// WebStudio PDF generator service using the shared base class
/// </summary>
public class WebStudioPdfService : BasePdfService<WebStudioPdfRequest, WebStudioPdfDocument>
{
    public WebStudioPdfService(HttpClient httpClient, IConfiguration configuration) : base(httpClient, configuration)
    {
    }

    protected override WebStudioPdfDocument CreateDocument(WebStudioPdfRequest request)
    {
        return new WebStudioPdfDocument(request, _httpClient, _configuration);
    }

    protected override string GetTitleFromRequest(WebStudioPdfRequest request)
    {
        return request.Title;
    }

    protected override string GetFileNameSuffix()
    {
        return "studio";
    }

    protected override string GetDefaultFileName()
    {
        return "WebStudio";
    }
}

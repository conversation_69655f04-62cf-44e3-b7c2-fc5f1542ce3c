using KPA.Services.QuestPDF.Shared;
using KPA.Services.QuestPDF.WebAboutUs;
using KPA.Services.QuestPDF.WebProject;
using QuestPDF.Companion;
using QuestPDF.Infrastructure;
using QuestPDF.Fluent;

namespace KPA.Services.QuestPDF.WebProfile;

/// <summary>
/// WebProfile PDF generator service that combines WebAboutUs and multiple WebProjects with a cover page
/// </summary>
public class WebProfilePdfService : IBasePdfService<WebProfilePdfRequest>
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly WebAboutUsPdfService _webAboutUsService;
    private readonly WebProjectPdfService _webProjectService;

    static WebProfilePdfService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    public WebProfilePdfService(
        HttpClient httpClient,
        IConfiguration configuration,
        WebAboutUsPdfService webAboutUsService,
        WebProjectPdfService webProjectService)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _webAboutUsService = webAboutUsService;
        _webProjectService = webProjectService;
    }

    /// <summary>
    /// Generate combined PDF with cover page, WebAboutUs, and multiple WebProjects
    /// </summary>
    public byte[] GeneratePdf(WebProfilePdfRequest request)
    {
        var pdfParts = new List<byte[]>();

        try
        {
            // 1. Add cover page if requested
            if (request.IncludeCoverPage)
            {
                var coverPageBytes = GetCoverPagePdf().GetAwaiter().GetResult();
                if (coverPageBytes != null)
                {
                    pdfParts.Add(coverPageBytes);
                }
            }

            // 2. Add WebAboutUs PDF if provided
            if (request.WebAboutUsRequest != null)
            {
                var aboutUsPdf = _webAboutUsService.GeneratePdf(request.WebAboutUsRequest);
                pdfParts.Add(aboutUsPdf);
            }

            // 3. Add WebProject PDFs
            foreach (var projectRequest in request.WebProjectRequests)
            {
                var projectPdf = _webProjectService.GeneratePdf(projectRequest);
                pdfParts.Add(projectPdf);
            }

            if (pdfParts.Count == 0)
            {
                throw new InvalidOperationException("No PDF content to generate. Please provide at least WebAboutUs or WebProject data.");
            }

            // 4. Merge all PDFs
            return PdfMerger.MergePdfs(pdfParts.ToArray());
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating WebProfile PDF: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Show combined PDF in QuestPDF Companion app
    /// </summary>
    public void ShowInCompanion(WebProfilePdfRequest request)
    {
        // For companion preview, we'll save the PDF to a temp file and open it
        var pdfBytes = GeneratePdf(request);
        var tempPath = Path.GetTempFileName().Replace(".tmp", ".pdf");

        try
        {
            File.WriteAllBytes(tempPath, pdfBytes);
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = tempPath,
                UseShellExecute = true
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error opening PDF in companion: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Generate filename for the combined PDF
    /// </summary>
    public string GenerateFileName(WebProfilePdfRequest request)
    {
        var fileName = !string.IsNullOrEmpty(request.CustomFileName)
            ? request.CustomFileName
            : $"{SanitizeFileName(request.Title)}_{DateTime.Now:yyyyMMdd_HHmmss}";

        return fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase)
            ? fileName
            : $"{fileName}.pdf";
    }

    /// <summary>
    /// Download cover page PDF from configuration URL
    /// </summary>
    private async Task<byte[]?> GetCoverPagePdf()
    {
        var coverPageUrl = _configuration["QuestPDF:CoverPageUrl"];
        if (string.IsNullOrEmpty(coverPageUrl))
        {
            Console.WriteLine("Cover page URL not configured in appsettings");
            return null;
        }

        return await PdfMerger.DownloadPdfAsync(_httpClient, coverPageUrl);
    }

    /// <summary>
    /// Sanitize filename to remove invalid characters
    /// </summary>
    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
    }
}

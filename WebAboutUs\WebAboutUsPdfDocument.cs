using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// WebAboutUs PDF document using the shared base class
/// </summary>
public class WebAboutUsPdfDocument : BasePdfDocument<WebAboutUsPdfRequest>
{

    public WebAboutUsPdfDocument(WebAboutUsPdfRequest request, HttpClient httpClient, IConfiguration configuration)
        : base(request, httpClient, configuration)
    {
        Console.WriteLine($"BrandColor: {_brandColor}");
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

    public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x =>
                x.FontSize(16)
                .FontFamily(Fonts.Calibri)
                .FontColor(Colors.Grey.Darken4)
                );

                page.Content()
                .Column(mainColumn =>
                {
                    mainColumn.Item().Element(ComposeAboutUs);
                    mainColumn.Item().Element(ComposeOurApproach);
                    mainColumn.Item().Element(ComposeOurServices);
                    mainColumn.Item().Element(ComposeOurExperience);
                });
            });
    }

    private void ComposeAboutUs(IContainer container)
    {
        container
        .Row(row =>
        {

            row.RelativeItem(1)
            .Extend()
            .BorderRight(1).BorderColor(Colors.Grey.Lighten1)
            .Column(column =>
            {
                // Title
                column.Item()
                .Padding(20)
                .Extend()
                .AlignMiddle()
                .Text("ABOUT US")
                    .FontSize(60)
                    // .AlignRight()
                    .Bold()
                    .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                // Description
                column.Item()
                .Padding(20)
                .Extend()
                .AlignMiddle()
                .Text(_request.AboutUs)
                .Justify();
            });
        });

    }

    private void ComposeOurApproach(IContainer container)
    {
        container
        .Row(row =>
        {
            row.RelativeItem(1)
         .Extend()
            .BorderRight(1).BorderColor(Colors.Grey.Lighten1)
            .Column(column =>
            {
                // Title
                column.Item()
                          .Padding(20)
                .Extend()
                .AlignMiddle()
                .Text("OUR APPROACH")
                   .FontSize(60)
                    // .AlignRight()
                    .Bold()
                    .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                // Description
                column.Item()
                             .Padding(20)
                .Extend()
                .AlignMiddle()
                .Text(_request.OurApproach)
                .Justify();
            });
        });
    }

    private void ComposeOurServices(IContainer container)
    {

        container
        .Row(row =>
        {
            row.RelativeItem(1)
            .Extend()
                    .BorderRight(1).BorderColor(Colors.Grey.Lighten1)
            .Column(column =>
            {
                // Title
                column.Item()
                             .Padding(20)
                .Extend()
                .AlignMiddle()
                .Text("OUR SERVICES")
                    .FontSize(60)
                    // .AlignRight()
                    .Bold()
                    .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                column.Item()
                                .Padding(20)
                .Extend()
                .AlignMiddle()
                .Column(column2 =>
                {
                    column2.Spacing(20);
                    foreach (var service in _request.Services)
                    {
                        column2.Item()
                        .Column(column3 =>
                        {
                            column3.Item()
                            .Text(service.Title)
                            .FontSize(30)
                            .Bold()
                            .FontColor(Colors.Grey.Darken3);

                            column3.Item()
                            .PaddingTop(10)
                            .Text(service.Description)
                            .Justify();
                        });
                    }
                });

            });
        });
    }

    private void ComposeImage(IContainer container, string title)
    {
        Console.WriteLine($"ComposeImage: {title}");
        var validImage = _request.Slides.FirstOrDefault(s => s.Title == title);
        if (validImage == null)
        {
            Console.WriteLine($"No image found for {title}");
            return;
        }

        if (_imageCache.TryGetValue(validImage.Url, out var imageBytes))
        {
            container
                    .Height(200)
                        .Image(imageBytes)
                        .FitArea();
        }
        else
        {
            ComposeImageFallback(container, "Image not found in cache");
        }
    }

    private void ComposeOurExperience(IContainer container)
    {
        container
        .Row(row =>
        {
            row.RelativeItem(1)
             .Extend()
            .BorderRight(1).BorderColor(Colors.Grey.Lighten1)
            .Column(column =>
            {
                // Title
                column.Item()
                .Padding(20)
                .Extend()
                .AlignMiddle()
                .Text("OUR EXPERIENCE")
                    .FontSize(60)
                     // .AlignRight()
                     .Bold()
                     .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                column.Item()
                .Padding(20)
                .Extend()
                .AlignMiddle()
                .Column(column2 =>
                {
                    
                            column2.Spacing(20);
                    foreach (var statistic in _request.Statistics)
                    {

                        column2.Item()
                        .Padding(10)
                        .Column(column3 =>
                        {

                            column3.Item()
                            .Text(t =>
                            {
                                t.Span(statistic.Value)
                                .FontSize(60)
                                .Bold()
                                .FontColor(Colors.Grey.Darken3);
                                if (!string.IsNullOrEmpty(statistic.Unit))
                                {
                                    t.Span(" ");

                                    t.Span(statistic.Unit)
                                    .FontSize(42)
                                      .Bold()
                                    .FontColor(Colors.Grey.Darken3);
                                }
                            });

                            column3.Item()
                            .Text(statistic.Label)
                            .FontSize(24)
                             .Bold()
                             .FontColor(Colors.Grey.Darken1);

                        });
                    }
                });


            });
        });
    }

}

﻿using KPA.Services.QuestPDF.Services;
using KPA.Services.QuestPDF.Shared;
using KPA.Services.QuestPDF.WebAboutUs;
using KPA.Services.QuestPDF.WebProject;
using KPA.Services.QuestPDF.WebProfile;
using KPA.Services.QuestPDF.WebStudio;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "KPA PDF Generation API", Version = "v1" });
});

// Register services
builder.Services.AddScoped<IAzureBlobService, AzureBlobService>();
builder.Services.AddScoped<WebProjectPdfService>();
builder.Services.AddScoped<WebAboutUsPdfService>();
builder.Services.AddScoped<WebProfilePdfService>();
builder.Services.AddScoped<WebStudioPdfService>();
builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Configure all PDF endpoints
app.ConfigurePdfEndpoints();

app.Run();

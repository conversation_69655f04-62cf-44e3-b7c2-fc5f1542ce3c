using KPA.Services.QuestPDF.Services;

namespace KPA.Services.QuestPDF.WebStudio;

public static class WebStudioEndpoints
{
    public static void MapWebStudioEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/WebStudio/pdf")
            .WithTags("WebStudio PDF")
            .WithOpenApi();

        group.MapPost("/generate-and-upload", GenerateAndUploadAsync)
            .WithName("GenerateAndUploadWebStudioPdf")
            .WithSummary("Generate WebStudio PDF and upload to Azure Blob Storage");

        group.MapPost("/generate-and-preview", GenerateAndPreview)
            .WithName("GenerateAndPreviewWebStudioPdf")
            .WithSummary("Generate WebStudio PDF and open in QuestPDF Companion");

        group.MapPost("/download", Download)
            .WithName("DownloadWebStudioPdf")
            .WithSummary("Download WebStudio PDF directly");
    }

    private static async Task<IResult> GenerateAndUploadAsync(
        WebStudioPdfRequest request,
        WebStudioPdfService pdfService,
        IAzureBlobService azureBlobService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting PDF generation and upload for project: {Title}", request.Title);

            // Validate request
            if (string.IsNullOrEmpty(request.Title))
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "Project title is required"
                });
            }

            if (string.IsNullOrEmpty(request.AzureConnectionString))
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "Azure connection string is required"
                });
            }

            // Generate PDF
            var pdfBytes = pdfService.GeneratePdf(request);
            logger.LogInformation("PDF generated successfully, size: {Size} bytes", pdfBytes.Length);

            // Generate filename
            var fileName = pdfService.GenerateFileName(request);

            // Upload to Azure Blob Storage
            var pdfUrl = await azureBlobService.UploadPdfAsync(
                pdfBytes,
                fileName,
                request.ContainerName,
                request.AzureConnectionString);

            logger.LogInformation("PDF uploaded successfully to: {PdfUrl}", pdfUrl);

            return Results.Ok(new PdfResponse
            {
                Success = true,
                PdfUrl = pdfUrl,
                FileName = fileName
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating or uploading PDF for project: {Title}", request.Title);

            return Results.Problem(new PdfResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            }.ErrorMessage);
        }
    }

    private static IResult GenerateAndPreview(
        WebStudioPdfRequest request,
        WebStudioPdfService pdfService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting PDF generation and preview for project: {Title}", request.Title);

            // Validate request
            if (string.IsNullOrEmpty(request.Title))
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "Project title is required"
                });
            }

            // Generate and show in companion app
            pdfService.ShowInCompanion(request);
            logger.LogInformation("PDF preview opened successfully for project: {Title}", request.Title);

            return Results.Ok(new PdfResponse
            {
                Success = true,
                FileName = pdfService.GenerateFileName(request)
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating PDF preview for project: {Title}", request.Title);

            return Results.Problem(new PdfResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            }.ErrorMessage);
        }
    }

    private static IResult Download(
        WebStudioPdfRequest request,
        WebStudioPdfService pdfService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting PDF download for project: {Title}", request.Title);

            // Validate request
            if (string.IsNullOrEmpty(request.Title))
            {
                return Results.BadRequest("Project title is required");
            }

            // Generate PDF
            var pdfBytes = pdfService.GeneratePdf(request);
            var fileName = pdfService.GenerateFileName(request);

            logger.LogInformation("PDF generated for download, size: {Size} bytes", pdfBytes.Length);

            return Results.File(pdfBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating PDF for download: {Title}", request.Title);
            return Results.Problem($"An error occurred: {ex.Message}");
        }
    }
}

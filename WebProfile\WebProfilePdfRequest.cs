
using KPA.Services.QuestPDF.WebAboutUs;
using KPA.Services.QuestPDF.WebProject;

namespace KPA.Services.QuestPDF.WebProfile;

/// <summary>
/// WebProfile PDF generation request containing combined data from WebAboutUs and multiple WebProjects
/// </summary>
public class WebProfilePdfRequest : PdfRequest
{
    /// <summary>
    /// WebAboutUs request data
    /// </summary>
    public WebAboutUsPdfRequest? WebAboutUsRequest { get; set; }

    /// <summary>
    /// Multiple WebProject request data
    /// </summary>
    public List<WebProjectPdfRequest> WebProjectRequests { get; set; } = new();

    /// <summary>
    /// Whether to include the cover page from appsettings URL
    /// </summary>
    public bool IncludeCoverPage { get; set; } = true;

    /// <summary>
    /// Custom title for the combined PDF
    /// </summary>
    public string Title { get; set; } = "Web Profile";
}

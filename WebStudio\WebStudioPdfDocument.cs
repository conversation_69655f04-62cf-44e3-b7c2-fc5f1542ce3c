using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebStudio;

/// <summary>
/// WebStudio PDF document using the shared base class
/// </summary>
public class WebStudioPdfDocument : BasePdfDocument<WebStudioPdfRequest>
{
    public WebStudioPdfDocument(WebStudioPdfRequest request, HttpClient httpClient, IConfiguration configuration)
        : base(request, httpClient, configuration)
    {
        Console.WriteLine($"BrandColor: {_brandColor}");
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

       public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(16).FontFamily(Fonts.Calibri).FontColor(Colors.Black));

                page.Content()
                // .Border(1).BorderColor(Colors.Orange.Accent1)
                .Column(mainColumn =>
                {
                    var validImages = _request.Slides.Select(s => s.Url).ToList();

                    if (validImages.Count == 1)
                    {
                        mainColumn.Item().Element(ComposeSingleImagePage);
                    }
                    else
                    {
                        mainColumn.Item().Element(ComposeMultiImagePage);
                    }

                });
            });
    }

    private void ComposeSingleImagePage(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();
                var imageOrientation = GetImageOrientation(imageUrl);

                if (imageOrientation == ImageOrientation.Landscape)
                {
                    // Vertical image: project details on top, image on bottom
                    container.Column(column =>
                    {
                        column.Item().Element(ComposeStudioDetails);
                        column.Item().PaddingTop(15).Element(ComposeMainImage);
                    });
                }
                else
                {
                    // Horizontal/Square image: project details on left, image on right
                    container.Row(row =>
                    {
                        row.RelativeItem(1).Element(ComposeStudioDetails);
                        row.RelativeItem(2).PaddingLeft(15)
                        .Element(ComposeMainImage);
                    });
                }
            }
            else
            {
                // No images available, just show project details
                container.Element(ComposeStudioDetails);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ComposeSingleImagePage: {ex.Message}");
            // Fallback to default horizontal layout
            container.Row(row =>
            {
                row.RelativeItem(1).Element(ComposeStudioDetails);
                row.RelativeItem(2).PaddingLeft(15).Element(ComposeMainImage);
            });
        }
    }

    private void ComposeMultiImagePage(IContainer container)
    {
        var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

        if (validImages.Count > 1)
        {
            var additionalImages = validImages.Skip(1).ToList();

            // Calculate how many additional images can fit on the first page in the right column
            // Assuming we can fit about 4-6 images in the right column on first page
            int imagesPerPageInRightColumn = 2; // Adjust this based on your layout needs

            container.Column(mainColumn =>
            {
                // First page with two-column layout
                mainColumn.Item().Row(row =>
                {
                    row.RelativeItem()
                    .Element(ComposeMultiImageColumn1);

                    row.RelativeItem().PaddingLeft(15)
                    .Element(container => ComposeAdditionalImagesForFirstPage(container, additionalImages, imagesPerPageInRightColumn));
                });

                // If there are remaining images, add them on subsequent pages with full width
                if (additionalImages.Count > imagesPerPageInRightColumn)
                {
                    var remainingImages = additionalImages.Skip(imagesPerPageInRightColumn).ToList();
                    mainColumn.Item().PageBreak();
                    mainColumn.Item().Element(container => ComposeRemainingImagesFullWidth(container, remainingImages));
                }
            });
        }
        else
        {
            // Fallback for single image
            container.Row(row =>
            {
                row.RelativeItem()
                .Element(ComposeMultiImageColumn1);

                row.RelativeItem().PaddingLeft(15)
                .Element(ComposeMultiImageColumn2);
            });
        }
    }

    private void ComposeMultiImageColumn1(IContainer container)
    {
        container
        .Column(column =>
            {
                column.Item().Element(ComposeStudioDetails);
                column.Item().PaddingTop(15).Element(ComposeMainImage);
            });
    }

    private void ComposeMultiImageColumn2(IContainer container)
    {
        container
        .Column(column =>
            {
                column.Item()
                .Element(ComposeAdditionalImages);
            });
    }

    private void ComposeStudioDetails(IContainer container)
    {
        container
        .Column(column =>
            {
                // Title
                column.Item()
                .Text(_request.Title)
                    .FontSize(60)
                    .Bold()
                    .FontColor(_brandColor);
            });
    }

    private void ComposeMainImage(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();

                // Use cached image bytes
                if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                {
                    container
                        .AlignBottom()
                        .AlignRight()
                        .Image(imageBytes)
                        .FitArea(); // This will use all available width and height
                }
                else
                {
                    ComposeImageFallback(container, "Image not found in cache");
                }
            }
            else
            {
                // Fallback when no images are available
                ComposeImageFallback(container, "No image available");
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading main image: {ex.Message}");
            ComposeImageFallback(container, "Image failed to load");
        }
    }

    private void ComposeAdditionalImages(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 1)
            {
                var additionalImages = validImages.Skip(1).ToList();

                if (additionalImages.Count == 1)
                {
                    // Console.WriteLine("Single additional image");
                    // Single additional image: full width
                    if (_imageCache.TryGetValue(additionalImages.First(), out var imageBytes))
                    {
                        container
                            .Image(imageBytes)
                            .FitArea(); // This will use all available width and height
                    }
                }
                else if (additionalImages.Count == 2)
                {
                    // Console.WriteLine("Two additional images");
                    // Two additional images: stack vertically
                    container
                        .Column(column =>
                        {
                            foreach (var imageUrl in additionalImages)
                            {
                                if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                {
                                    column.Item()
                                        .PaddingBottom(15)
                                        .Image(imageBytes)
                                        .FitArea(); // This will use all available width and height
                                }
                            }
                        });
                }
                else if (additionalImages.Count >= 3)
                {
                    // Console.WriteLine("Three or more additional images");
                    // 2 columns for 3 or more images
                    container
                        .Column(column =>
                        {
                            column.Spacing(15);
                            for (int i = 0; i < additionalImages.Count; i += 2)
                            {
                                column.Item()
                                .Row(row =>
                                {
                                    row.Spacing(15);
                                    for (int j = 0; j < 2; j++)
                                    {
                                        if (i + j < additionalImages.Count)
                                        {
                                            var imageUrl = additionalImages[i + j];
                                            if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                            {
                                                if (j == 0)
                                                {
                                                    row.RelativeItem()
                                                        // .PaddingBottom(15)
                                                        .Image(imageBytes)
                                                        .FitArea(); // This will use all available width and height
                                                }
                                                else
                                                {
                                                    row.RelativeItem()
                                                        // .PaddingBottom(15)
                                                        // .PaddingLeft(15)
                                                        .Image(imageBytes)
                                                        .FitArea(); // This will use all available width and height
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                        });
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading additional images: {ex.Message}");
        }
    }

    private void ComposeAdditionalImagesForFirstPage(IContainer container, List<string> additionalImages, int maxImages)
    {
        try
        {
            var imagesToShow = additionalImages.Take(maxImages).ToList();

            if (imagesToShow.Count == 1)
            {
                // Single additional image: full width
                if (_imageCache.TryGetValue(imagesToShow.First(), out var imageBytes))
                {
                    container
                        .Image(imageBytes)
                        .FitArea();
                }
            }
            else if (imagesToShow.Count == 2)
            {
                // Two additional images: stack vertically
                container
                    .Column(column =>
                    {
                        foreach (var imageUrl in imagesToShow)
                        {
                            if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                            {
                                column.Item()
                                    .PaddingBottom(15)
                                    .Image(imageBytes)
                                    .FitArea();
                            }
                        }
                    });
            }
            else if (imagesToShow.Count >= 3)
            {
                // 2 columns for 3 or more images
                container
                    .Column(column =>
                    {
                        column.Spacing(15);
                        for (int i = 0; i < imagesToShow.Count; i += 2)
                        {
                            column.Item()
                            .Row(row =>
                            {
                                row.Spacing(15);
                                for (int j = 0; j < 2; j++)
                                {
                                    if (i + j < imagesToShow.Count)
                                    {
                                        var imageUrl = imagesToShow[i + j];
                                        if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                        {
                                            row.RelativeItem()
                                                .Image(imageBytes)
                                                .FitArea();
                                        }
                                    }
                                }
                            });
                        }
                    });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading additional images for first page: {ex.Message}");
        }
    }

    private void ComposeRemainingImagesFullWidth(IContainer container, List<string> remainingImages)
    {
        try
        {
            container
                .Column(column =>
                {
                    column.Spacing(15);

                    // Arrange images in a grid with 3-4 columns for full width utilization
                    int imagesPerRow = 2; // You can adjust this based on your preference

                    for (int i = 0; i < remainingImages.Count; i += imagesPerRow)
                    {
                        column.Item()
                        .Row(row =>
                        {
                            row.Spacing(15);
                            for (int j = 0; j < imagesPerRow; j++)
                            {
                                if (i + j < remainingImages.Count)
                                {
                                    var imageUrl = remainingImages[i + j];
                                    if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                    {
                                        row.RelativeItem()
                                            .Image(imageBytes)
                                            .FitArea();
                                    }
                                }
                                else
                                {
                                    // Add empty space to maintain grid alignment
                                    row.RelativeItem();
                                }
                            }
                        });
                    }
                });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading remaining images: {ex.Message}");
        }
    }
}

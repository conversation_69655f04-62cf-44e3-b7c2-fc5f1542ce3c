using KPA.Services.QuestPDF.Services;

namespace KPA.Services.QuestPDF.WebProfile;

public static class WebProfileEndpoints
{
    public static void MapWebProfileEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/WebProfile/pdf")
            .WithTags("WebProfile PDF")
            .WithOpenApi();

        group.MapPost("/generate-and-upload", GenerateAndUploadAsync)
            .WithName("GenerateAndUploadWebProfilePdf")
            .WithSummary("Generate WebProfile PDF and upload to Azure Blob Storage");

        group.MapPost("/generate-and-preview", GenerateAndPreview)
            .WithName("GenerateAndPreviewWebProfilePdf")
            .WithSummary("Generate WebProfile PDF and open in QuestPDF Companion");

        group.MapPost("/download", Download)
            .WithName("DownloadWebProfilePdf")
            .WithSummary("Download WebProfile PDF directly");
    }

    private static async Task<IResult> GenerateAndUploadAsync(
        WebProfilePdfRequest request,
        WebProfilePdfService pdfService,
        IAzureBlobService azureBlobService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting WebProfile PDF generation and upload");

            if (string.IsNullOrEmpty(request.AzureConnectionString))
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "Azure connection string is required"
                });
            }

            // Validate request has content
            if (request.WebAboutUsRequest == null && request.WebProjectRequests.Count == 0)
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "At least WebAboutUs or WebProject data is required"
                });
            }

            // Generate PDF
            var pdfBytes = pdfService.GeneratePdf(request);
            logger.LogInformation("WebProfile PDF generated successfully, size: {Size} bytes", pdfBytes.Length);

            // Generate filename
            var fileName = pdfService.GenerateFileName(request);

            // Upload to Azure Blob Storage
            var pdfUrl = await azureBlobService.UploadPdfAsync(
                pdfBytes,
                fileName,
                request.ContainerName,
                request.AzureConnectionString);

            logger.LogInformation("WebProfile PDF uploaded successfully to: {PdfUrl}", pdfUrl);

            return Results.Ok(new PdfResponse
            {
                Success = true,
                PdfUrl = pdfUrl,
                FileName = fileName
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating or uploading WebProfile PDF: {Title}", request.Title);

            return Results.Problem(new PdfResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            }.ErrorMessage);
        }
    }

    private static IResult GenerateAndPreview(
        WebProfilePdfRequest request,
        WebProfilePdfService pdfService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting WebProfile PDF generation and preview: {Title}", request.Title);

            // Validate request has content
            if (request.WebAboutUsRequest == null && request.WebProjectRequests.Count == 0)
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "At least WebAboutUs or WebProject data is required"
                });
            }

            // Generate and show in companion app
            pdfService.ShowInCompanion(request);
            logger.LogInformation("WebProfile PDF preview opened successfully: {Title}", request.Title);

            return Results.Ok(new PdfResponse
            {
                Success = true,
                FileName = pdfService.GenerateFileName(request)
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating WebProfile PDF preview: {Title}", request.Title);

            return Results.Problem(new PdfResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            }.ErrorMessage);
        }
    }

    private static IResult Download(
        WebProfilePdfRequest request,
        WebProfilePdfService pdfService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting WebProfile PDF download: {Title}", request.Title);

            // Validate request has content
            if (request.WebAboutUsRequest == null && request.WebProjectRequests.Count == 0)
            {
                return Results.BadRequest("At least WebAboutUs or WebProject data is required");
            }

            // Generate PDF
            var pdfBytes = pdfService.GeneratePdf(request);
            var fileName = pdfService.GenerateFileName(request);

            logger.LogInformation("WebProfile PDF generated for download, size: {Size} bytes", pdfBytes.Length);

            return Results.File(pdfBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating WebProfile PDF for download: {Title}", request.Title);
            return Results.Problem($"An error occurred: {ex.Message}");
        }
    }
}

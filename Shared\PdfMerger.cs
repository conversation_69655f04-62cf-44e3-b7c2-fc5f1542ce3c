using PdfSharp.Pdf;
using PdfSharp.Pdf.IO;

namespace KPA.Services.QuestPDF.Shared;

/// <summary>
/// Utility class for merging multiple PDF documents
/// </summary>
public static class PdfMerger
{
    /// <summary>
    /// Merges multiple PDF byte arrays into a single PDF
    /// </summary>
    /// <param name="pdfByteArrays">List of PDF byte arrays to merge</param>
    /// <returns>Merged PDF as byte array</returns>
    public static byte[] MergePdfs(params byte[][] pdfByteArrays)
    {
        if (pdfByteArrays == null || pdfByteArrays.Length == 0)
            throw new ArgumentException("At least one PDF is required for merging");

        using var outputDocument = new PdfDocument();
        
        foreach (var pdfBytes in pdfByteArrays)
        {
            if (pdfBytes == null || pdfBytes.Length == 0)
                continue;

            try
            {
                using var inputStream = new MemoryStream(pdfBytes);
                using var inputDocument = PdfReader.Open(inputStream, PdfDocumentOpenMode.Import);
                
                // Copy all pages from the input document to the output document
                for (int pageIndex = 0; pageIndex < inputDocument.PageCount; pageIndex++)
                {
                    var page = inputDocument.Pages[pageIndex];
                    outputDocument.AddPage(page);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error merging PDF: {ex.Message}");
                // Continue with other PDFs even if one fails
            }
        }

        if (outputDocument.PageCount == 0)
            throw new InvalidOperationException("No valid pages found in any of the provided PDFs");

        using var outputStream = new MemoryStream();
        outputDocument.Save(outputStream);
        return outputStream.ToArray();
    }

    /// <summary>
    /// Downloads a PDF from a URL and returns it as byte array
    /// </summary>
    /// <param name="httpClient">HttpClient instance</param>
    /// <param name="pdfUrl">URL of the PDF to download</param>
    /// <returns>PDF as byte array</returns>
    public static async Task<byte[]?> DownloadPdfAsync(HttpClient httpClient, string pdfUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(pdfUrl))
                return null;

            var response = await httpClient.GetAsync(pdfUrl);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsByteArrayAsync();
            }
            else
            {
                Console.WriteLine($"Failed to download PDF: {response.StatusCode} - {pdfUrl}");
                return null;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error downloading PDF from {pdfUrl}: {ex.Message}");
            return null;
        }
    }
}

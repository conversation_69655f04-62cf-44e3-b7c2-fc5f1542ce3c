@KPA.Services.QuestPDF_HostAddress = http://localhost:5246

### Generate WebStudio PDF and upload to Azure Blob Storage
POST {{KPA.Services.QuestPDF_HostAddress}}/api/WebStudio/pdf/generate-and-upload
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "studio.pdf",
    "title":"STUDIO",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/01-046A4185-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  }

    ]
   }

###

### Generate WebStudio PDF and preview in QuestPDF Companion (Single Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/WebStudio/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "studio.pdf",
    "title":"STUDIO",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  },
                  
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/01-046A4185-HDR.jpg",
                       "orderFlag": 1
                  },
                  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  },
                                      {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  },
                                      {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  },
                  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  },
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  },
                                      {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  },
                                      {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  }

    ]
   }

###


### Generate WebStudio PDF and preview in QuestPDF Companion (3 Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/WebStudio/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "studio.pdf",
    "title":"STUDIO",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/01-046A4185-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  }

    ]
   }
###

### Generate WebStudio PDF and preview in QuestPDF Companion (3 Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/WebStudio/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "studio.pdf",
    "title":"STUDIO",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/01-046A4185-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  }

    ]
   }

###

### Generate WebStudio PDF and preview in QuestPDF Companion
POST {{KPA.Services.QuestPDF_HostAddress}}/api/WebStudio/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "studio.pdf",
    "title":"STUDIO",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/01-046A4185-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  }

    ]
   }
###

### Download WebStudio PDF directly
POST {{KPA.Services.QuestPDF_HostAddress}}/api/WebStudio/pdf/download
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "studio.pdf",
    "title":"STUDIO",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4303-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/01-046A4185-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/02-046A4225-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHITECTS/046A4180-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4220-HDR.jpg",
                       "orderFlag": 1
                  }
                    {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebContact/KAUSHIK%20PATEL%20ARCHTECTS/046A4268-HDR.jpg",
                       "orderFlag": 1
                  }

    ]
   }

###
